"use client"

import React, { useState } from "react"
import { useRouter } from "next/navigation"
import { AlertCircle, CheckCircle2 } from "lucide-react"
import { toast } from "react-toastify"

import { trpc } from "@/lib/trpc/client"
import { getBrowserInfo } from "@/lib/utils/browser-info"
import { Button } from "@nextui-org/button"
import { Checkbox } from "@nextui-org/checkbox"
import { Card, CardBody } from "@nextui-org/card"

export default function RenewalForm({
  token,
  subscriptionId,
  price,
  periodText,
}: {
  token: string
  subscriptionId: string
  price: number
  periodText: string
}) {
  const router = useRouter()
  const [error, setError] = useState<string | null>(null)
  const [consentGiven, setConsentGiven] = useState(false)
  const [termsAccepted, setTermsAccepted] = useState(false)

  const processRenewal = trpc.subscription.processRenewal.useMutation({
    onSuccess: (data) => {
      setError(null)
      toast.success("Renouvellement traité avec succès !")
      
      // Redirect to success page or subscription dashboard
      setTimeout(() => {
        router.push("/subscription/success?renewal=true")
      }, 2000)
    },
    onError: (error) => {
      const errorMsg = error.message || "Une erreur est survenue lors du renouvellement."
      setError(errorMsg)
      toast.error(errorMsg)
    },
  })

  const handleRenewalSubmit = () => {
    setError(null)

    if (!consentGiven) {
      setError("Veuillez donner votre consentement pour le renouvellement.")
      return
    }

    if (!termsAccepted) {
      setError("Veuillez accepter les conditions générales.")
      return
    }

    // Get browser information for compliance
    const browserInfos = getBrowserInfo()

    processRenewal.mutate({
      token,
      browserInfos,
    })
  }

  const formattedPrice = Intl.NumberFormat("fr-FR", { 
    style: "currency", 
    currency: "EUR" 
  }).format(price / 100)

  return (
    <div className="space-y-6">
      {error && (
        <div className="flex w-full items-center gap-2 rounded-lg border border-danger-200 bg-danger-50 p-3">
          <AlertCircle className="size-5 shrink-0 text-danger" />
          <p className="text-sm text-danger">{error}</p>
        </div>
      )}

      {/* Consent Section */}
      <Card className="border-primary-200 bg-primary-50 dark:bg-primary-100/20">
        <CardBody className="space-y-4 p-4">
          <h3 className="font-semibold text-primary-700 dark:text-primary-600">
            Consentement requis
          </h3>
          
          <div className="space-y-3">
            <Checkbox
              isSelected={consentGiven}
              onValueChange={setConsentGiven}
              color="primary"
              size="sm"
            >
              <span className="text-sm text-primary-700 dark:text-primary-600">
                Je consens expressément au renouvellement automatique de mon abonnement 
                pour la période suivante au prix de <strong>{formattedPrice}/{periodText}</strong>.
              </span>
            </Checkbox>

            <Checkbox
              isSelected={termsAccepted}
              onValueChange={setTermsAccepted}
              color="primary"
              size="sm"
            >
              <span className="text-sm text-primary-700 dark:text-primary-600">
                J'accepte les{" "}
                <a 
                  href="/terms" 
                  target="_blank" 
                  className="underline hover:text-primary-800"
                >
                  conditions générales
                </a>{" "}
                et la{" "}
                <a 
                  href="/privacy" 
                  target="_blank" 
                  className="underline hover:text-primary-800"
                >
                  politique de confidentialité
                </a>.
              </span>
            </Checkbox>
          </div>
        </CardBody>
      </Card>

      {/* Information Section */}
      <div className="rounded-lg bg-default-100 p-4 text-sm text-default-600">
        <h4 className="mb-2 font-medium text-default-700">Informations importantes :</h4>
        <ul className="space-y-1 text-xs">
          <li>• Ce renouvellement est nécessaire pour maintenir l'accès à votre abonnement</li>
          <li>• Le paiement sera traité de manière sécurisée via notre partenaire MangoPay</li>
          <li>• Vous recevrez une confirmation par email une fois le renouvellement effectué</li>
          <li>• Vous pouvez annuler votre abonnement à tout moment depuis votre profil</li>
        </ul>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
        <Button
          variant="bordered"
          onPress={() => router.push("/")}
          isDisabled={processRenewal.isPending}
          className="w-full sm:w-auto"
        >
          Annuler
        </Button>
        
        <Button
          color="primary"
          size="lg"
          onPress={handleRenewalSubmit}
          isLoading={processRenewal.isPending}
          isDisabled={!consentGiven || !termsAccepted || processRenewal.isPending}
          className="w-full px-8 font-semibold sm:w-auto"
          startContent={!processRenewal.isPending ? <CheckCircle2 className="size-4" /> : undefined}
        >
          {processRenewal.isPending 
            ? "Traitement en cours..." 
            : `Confirmer le renouvellement - ${formattedPrice}`
          }
        </Button>
      </div>

      {/* Legal Notice */}
      <div className="text-center text-xs text-default-500">
        <p>
          En cliquant sur "Confirmer le renouvellement", vous autorisez le prélèvement 
          du montant indiqué selon les modalités de votre abonnement.
        </p>
        <p className="mt-1">
          Cette transaction est sécurisée et conforme aux réglementations européennes (PSD2).
        </p>
      </div>
    </div>
  )
}
