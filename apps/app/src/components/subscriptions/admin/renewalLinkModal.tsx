"use client"

import React, { useState } from "react"
import { toast } from "react-toastify"
import { Co<PERSON>, ExternalLink, Calendar, User, CreditCard } from "lucide-react"

import { trpc } from "@/lib/trpc/client"
import { env } from "@/lib/env"
import { <PERSON><PERSON> } from "@nextui-org/button"
import { <PERSON><PERSON>, <PERSON>dalBody, ModalContent, Modal<PERSON>ooter, ModalHeader } from "@nextui-org/modal"
import { Card, CardBody, CardHeader } from "@nextui-org/card"
import { Chip } from "@nextui-org/chip"
import { Input } from "@nextui-org/input"
import { Divider } from "@nextui-org/divider"
import { Prisma } from "@prisma/client"

type SubscriptionWithUserAndPlan = Prisma.SubscriptionGetPayload<{
  include: {
    user: {
      select: {
        id: true
        name: true
        email: true
      }
    }
    plan: true
  }
}>

interface RenewalLinkModalProps {
  isOpen: boolean
  onClose: () => void
  subscription: SubscriptionWithUserAndPlan
}

const RenewalLinkModal = ({ isOpen, onClose, subscription }: RenewalLinkModalProps) => {
  const [renewalLink, setRenewalLink] = useState<string>("")
  const [linkGenerated, setLinkGenerated] = useState(false)

  const generateRenewalLink = trpc.subscription.generateRenewalLink.useMutation({
    onSuccess: (data) => {
      const fullUrl = `${env.NEXT_PUBLIC_BASE_URL}${data.renewalUrl}`
      setRenewalLink(fullUrl)
      setLinkGenerated(true)
      toast.success("Lien de renouvellement généré avec succès")
    },
    onError: (error) => {
      toast.error(`Erreur lors de la génération: ${error.message}`)
    },
  })

  const handleGenerateLink = () => {
    generateRenewalLink.mutate({
      subscriptionId: subscription.id,
    })
  }

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(renewalLink)
      toast.success("Lien copié dans le presse-papiers")
    } catch (error) {
      toast.error("Erreur lors de la copie")
    }
  }

  const handleOpenLink = () => {
    window.open(renewalLink, "_blank")
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString("fr-FR", {
      year: "numeric",
      month: "long",
      day: "numeric",
    })
  }

  const formatPrice = (price: number) => {
    return Intl.NumberFormat("fr-FR", {
      style: "currency",
      currency: "EUR",
    }).format(price / 100)
  }

  const price = subscription.billingPeriod === "MONTHLY" 
    ? subscription.plan.monthlyPrice 
    : subscription.plan.annualPrice

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="2xl">
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1">
              Générer un lien de renouvellement
            </ModalHeader>
            <ModalBody>
              {/* Subscription Details */}
              <Card className="border-none bg-default-50">
                <CardHeader>
                  <h3 className="text-lg font-semibold">Détails de l&apos;abonnement</h3>
                </CardHeader>
                <CardBody className="space-y-4">
                  <div className="flex items-center gap-3">
                    <User className="size-5 text-default-500" />
                    <div>
                      <p className="font-medium">{subscription.user.name}</p>
                      <p className="text-sm text-default-500">{subscription.user.email}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <CreditCard className="size-5 text-default-500" />
                    <div>
                      <p className="font-medium">{subscription.plan.name}</p>
                      <p className="text-sm text-default-500">
                        {formatPrice(price)} / {subscription.billingPeriod === "MONTHLY" ? "mois" : "an"}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Calendar className="size-5 text-default-500" />
                    <div>
                      <p className="text-sm text-default-500">Expire le</p>
                      <p className="font-medium">{formatDate(subscription.endDate)}</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Chip 
                      color={subscription.status === "AUTHENTICATION_NEEDED" ? "secondary" : "default"}
                      variant="flat"
                      size="sm"
                    >
                      {subscription.status === "AUTHENTICATION_NEEDED" ? "Authentification requise" : subscription.status}
                    </Chip>
                  </div>
                </CardBody>
              </Card>

              <Divider />

              {/* Link Generation */}
              {!linkGenerated ? (
                <div className="text-center space-y-4">
                  <div className="rounded-lg bg-warning-50 p-4 dark:bg-warning-100/20">
                    <h4 className="font-medium text-warning-700 dark:text-warning-600">
                      ⚠️ Génération de lien de renouvellement
                    </h4>
                    <p className="mt-2 text-sm text-warning-600 dark:text-warning-500">
                      Ce lien permettra à l&apos;utilisateur de renouveler son abonnement de manière sécurisée.
                      Le lien expirera dans 30 jours et un email sera automatiquement envoyé à l&apos;utilisateur.
                    </p>
                  </div>

                  <Button
                    color="primary"
                    size="lg"
                    onPress={handleGenerateLink}
                    isLoading={generateRenewalLink.isPending}
                    className="w-full"
                  >
                    Générer le lien de renouvellement
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="rounded-lg bg-success-50 p-4 dark:bg-success-100/20">
                    <h4 className="font-medium text-success-700 dark:text-success-600">
                      ✅ Lien généré avec succès
                    </h4>
                    <p className="mt-2 text-sm text-success-600 dark:text-success-500">
                      Le lien de renouvellement a été généré et un email a été envoyé à l&apos;utilisateur.
                    </p>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-default-700">
                      Lien de renouvellement :
                    </label>
                    <div className="flex gap-2">
                      <Input
                        value={renewalLink}
                        readOnly
                        className="flex-1"
                        size="sm"
                      />
                      <Button
                        isIconOnly
                        size="sm"
                        variant="flat"
                        onPress={handleCopyLink}
                        className="shrink-0"
                      >
                        <Copy size={16} />
                      </Button>
                      <Button
                        isIconOnly
                        size="sm"
                        variant="flat"
                        color="primary"
                        onPress={handleOpenLink}
                        className="shrink-0"
                      >
                        <ExternalLink size={16} />
                      </Button>
                    </div>
                  </div>

                  <div className="text-xs text-default-500">
                    <p>• Le lien expire dans 30 jours</p>
                    <p>• L&apos;utilisateur recevra un email avec ce lien</p>
                    <p>• Le lien ne peut être utilisé qu&apos;une seule fois</p>
                  </div>
                </div>
              )}
            </ModalBody>
            <ModalFooter>
              <Button color="danger" variant="light" onPress={onClose}>
                Fermer
              </Button>
              {linkGenerated && (
                <Button
                  color="primary"
                  onPress={handleGenerateLink}
                  isLoading={generateRenewalLink.isPending}
                >
                  Générer un nouveau lien
                </Button>
              )}
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  )
}

export default RenewalLinkModal
